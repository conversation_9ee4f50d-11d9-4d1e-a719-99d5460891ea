import React, { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { UPDATE_AGENT } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const AGENT_ROLES = ['USER', 'ADMIN', 'DEVELOPER'];
const CLEARANCE_LEVELS = [
  'CL0', 'CL1', 'CL2', 'CL3', 'CL4', 'CL5',
  'CL6', 'CL7', 'CL8', 'CL9', 'CLS', 'CLX'
];

const ManageAgentAccessForm = ({ onSubmit, onCancel, currentRole, currentClearance, agentUsername }) => {
  const [role, setRole] = useState(currentRole || 'USER');
  const [clearance, setClearance] = useState(currentClearance || 'CL2');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    setRole(currentRole || 'USER');
    setClearance(currentClearance || 'CL2');
  }, [currentRole, currentClearance]);

  const handleInternalSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    
    if (!role.trim()) {
      setErrorMessage('Role is required.');
      return;
    }
    
    if (!clearance.trim()) {
      setErrorMessage('Clearance level is required.');
      return;
    }
    
    onSubmit({ role: role.trim(), clearance: clearance.trim() });
  };

  return (
    <form onSubmit={handleInternalSubmit}>
      <div className={styles.modalHeader}>
        <h4>Manage Access for {agentUsername}</h4>
      </div>
      <div className={styles.modalBody}>
        {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
        <div className={styles.formGroup}>
          <label htmlFor="agentRole">Role:</label>
          <select
            id="agentRole"
            value={role}
            onChange={(e) => setRole(e.target.value)}
            className={styles.darkInput}
            required
          >
            {AGENT_ROLES.map(r => (
              <option key={r} value={r}>{r}</option>
            ))}
          </select>
        </div>
        <div className={styles.formGroup}>
          <label htmlFor="agentClearance">Clearance Level:</label>
          <select
            id="agentClearance"
            value={clearance}
            onChange={(e) => setClearance(e.target.value)}
            className={styles.darkInput}
            required
          >
            {CLEARANCE_LEVELS.map(level => (
              <option key={level} value={level}>{level}</option>
            ))}
          </select>
        </div>
      </div>
      <div className={styles.modalFooter}>
        <button type="button" className={styles.secondary} onClick={onCancel}>
          Cancel
        </button>
        <button type="submit" className={styles.primary}>
          Update Access
        </button>
      </div>
    </form>
  );
};

const ManageAgentAccessModal = ({ isOpen, onClose, agent, onRefetch }) => {
  const [updateAgentMutation, { loading }] = useMutation(UPDATE_AGENT, {
    onCompleted: () => {
      onRefetch();
      onClose();
      toast.success('Agent access updated successfully!');
    },
    onError: (error) => {
      toast.error(error.message || 'Could not update agent access. Please try again.');
    }
  });

  if (!isOpen || !agent) return null;

  const handleFormSubmitRelay = (formData) => {
    updateAgentMutation({
      variables: {
        hiveId: agent.hiveId,
        role: formData.role,
        clearance: formData.clearance
      }
    });
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose} disabled={loading}>
          &times;
        </button>
        
        <ManageAgentAccessForm
          onSubmit={handleFormSubmitRelay}
          onCancel={handleClose}
          currentRole={agent.role}
          currentClearance={agent.clearance}
          agentUsername={agent.username}
        />
      </div>
    </div>
  );
};

export default ManageAgentAccessModal;
