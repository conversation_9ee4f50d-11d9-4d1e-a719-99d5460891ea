import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_AGENT } from '../../../api/mutations';
import { GET_AGENTS } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const AGENT_ROLES = ['USER', 'ADMIN', 'DEVELOPER'];
const CLEARANCE_LEVELS = [
  'CL0', 'CL1', 'CL2', 'CL3', 'CL4', 'CL5',
  'CL6', 'CL7', 'CL8', 'CL9', 'CLS', 'CLX'
];

const CreateAgentModal = ({ isOpen, onClose }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState(AGENT_ROLES[0]); // Default to USER (first/lowest role)
  const [clearance, setClearance] = useState('CL2'); // Default to CL2 (All agents)
  const [backingPersonHiveId, setBackingPersonHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [createAgent, { loading }] = useMutation(CREATE_AGENT, {
    refetchQueries: [{ query: GET_AGENTS }],
    onError: (error) => {
      setErrorMessage(error.graphQLErrors && error.graphQLErrors.length > 0 ? error.graphQLErrors[0].message : error.message || 'Could not create agent. Please try again.');
      toast.error(error.graphQLErrors && error.graphQLErrors.length > 0 ? error.graphQLErrors[0].message : error.message || 'Could not create agent. Please try again.');
    },
    onCompleted: () => {
      onClose();
      setUsername('');
      setPassword('');
      setRole(AGENT_ROLES[0]); 
      setClearance('CL2'); 
      setBackingPersonHiveId('');
      setErrorMessage('');
      toast.success('Agent created successfully!');
    },
  });

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!username.trim() || !password.trim() || !role || !clearance || !backingPersonHiveId.trim()) {
      setErrorMessage('All fields are required.');
      return;
    }
    try {
      await createAgent({ variables: { username, password, role, clearance, backingPersonHiveId } });
    } catch (err) {
      console.error('Submission error:', err);
      if (!errorMessage) {
        setErrorMessage('An unexpected error occurred during submission.');
      }
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Agent</h3>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p style={{ color: 'red' }}>{errorMessage}</p>}
            <div>
              <label htmlFor="agentUsername">Username:</label>
              <input
                type="text"
                id="agentUsername"
                className={styles.darkInput}
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="agentPassword">Password:</label>
              <input
                type="password"
                id="agentPassword"
                className={styles.darkInput}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="agentRole">Role:</label>
              <select
                id="agentRole"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                required
                disabled={loading}
                className={styles.darkInput}
              >
                {AGENT_ROLES.map(r => <option key={r} value={r}>{r}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="agentClearance">Clearance Level:</label>
              <select
                id="agentClearance"
                value={clearance}
                onChange={(e) => setClearance(e.target.value)}
                required
                disabled={loading}
                className={styles.darkInput}
              >
                {CLEARANCE_LEVELS.map(cl => <option key={cl} value={cl}>{cl}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="backingPersonHiveId">Backing Person Hive ID:</label>
              <input
                type="text"
                id="backingPersonHiveId"
                className={styles.darkInput}
                value={backingPersonHiveId}
                onChange={(e) => setBackingPersonHiveId(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onClose} disabled={loading}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Agent'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateAgentModal; 