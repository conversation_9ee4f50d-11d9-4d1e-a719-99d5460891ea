const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const { validateDateOfBirth, validateBackingPersonUniqueness, validateNonEmptyString, validateParentOrganization, validateOperationExists, validateAgentExists, validateInformantExists, validateCaseExists, validateTargetExists, validateTargetRelationshipExists, validateCaseScopeRelationshipExists, validateTaskExists, validateTaskScopeEntityExists, validateTaskPriority, validateClearanceLevel, validateAgentRole, validateCaseStatus } = require('./validations');
const { GraphQLError } = require('graphql');

// Get JWT secret from environment variables
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('[ERROR] JWT_SECRET environment variable is not set');
  process.exit(1);
}

const resolvers = {
  VehicleOwner: {
    __resolveType(obj, context, info){
      if(obj.firstName || obj.lastName || obj.dateOfBirth){
        return 'Person';
      }
      if(obj.name || obj.foundingDate){
        return 'Organization';
      }
      return null;
    },
  },
  OperationTarget: {
    __resolveType(obj, context, info){
      if(obj.firstName || obj.lastName || obj.dateOfBirth){
        return 'Person';
      }
      if(obj.name || obj.foundingDate){
        return 'Organization';
      }
      if(obj.type || obj.make || obj.model || obj.color){
        return 'Vehicle';
      }
      return null;
    },
  },
  TaskScope: {
    __resolveType(obj, context, info){
      if (obj.__typename) {
        return obj.__typename;
      }

      if (obj.__labels) {
        if (obj.__labels.includes('Case')) {
          return 'Case';
        }
        if (obj.__labels.includes('Operation')) {
          return 'Operation';
        }
      }
      return null;
    },
  },
  Task: {
    scope: async (parent, args, context, info) => {
      const { driver } = context;
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $taskHiveId})
            OPTIONAL MATCH (t)-[:SCOPED_TO]->(scope)
            WHERE scope:Case OR scope:Operation
            RETURN scope {
              __typename: CASE WHEN scope:Case THEN 'Case' ELSE 'Operation' END,
              .hiveId,
              .title,
              .creationDate,
              .status,
              .type
            }
          `,
          { taskHiveId: parseInt(parent.hiveId, 10) }
        );

        return records.length > 0 && records[0].get('scope') ? [records[0].get('scope')] : [];
      } finally {
        await session.close();
      }
    },
  },
  Mutation: {

  // Create

    async createOrganization(
      _parent, { name, foundingDate }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (o:Organization { hiveId: newValue, name: $name, foundingDate: $foundingDate })
            RETURN o { .hiveId, .name, .foundingDate }
          `,
          { name, foundingDate }
        );
        return records[0].get('o');
      } finally {
        await session.close();
      }
    },

    async createPerson(
      _parent, { firstName, lastName, dateOfBirth }, { driver }) {

      // --- Validation ---
      validateDateOfBirth(dateOfBirth);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (p:Person { hiveId: newValue, firstName: $firstName, lastName: $lastName, dateOfBirth: $dateOfBirth })
            RETURN p { .hiveId, .firstName, .lastName, .dateOfBirth }
          `,
          { firstName, lastName, dateOfBirth }
        );
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async createCase(
      _parent, { title }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (c:Case { hiveId: newValue, title: $title, creationDate: date(), status: "OPEN" })
            RETURN c { .hiveId, .title, .creationDate, .status }
          `,
          { title }
        );
        const caseObj = records[0].get('c');
        return caseObj;
      } finally {
        await session.close();
      }
    },

    async createAgent(
      _parent, { username, password, role, backingPersonHiveId, clearance }, { driver }) {

      // --- Validation ---
      await validateBackingPersonUniqueness(backingPersonHiveId, driver, 'Agent');
      if (clearance) {
        validateClearanceLevel(clearance);
      }
      // --- End Validation ---

      const session = driver.session();
      try {
        const hashedPassword = await bcrypt.hash(password, 10);
        const agentClearance = clearance || 'CL2'; // Default to CL2 if not provided
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            MATCH (p:Person {hiveId: $backingPersonHiveId})
            CREATE (a:Agent { hiveId: newValue, username: $username, password: $password, role: $role, clearance: $clearance })
            CREATE (a)-[:BACKED_BY { since: date() }]->(p)
            RETURN a {
              .hiveId,
              .username,
              .role,
              .clearance,
              backingPerson: [p { .hiveId, .firstName, .lastName }]
            }
          `,
          { username, password: hashedPassword, role, backingPersonHiveId: parseInt(backingPersonHiveId, 10), clearance: agentClearance }
        );
        if (records.length === 0) {
          throw new GraphQLError('Could not create agent or find backing person.', {
            extensions: { code: 'AGENT_CREATION_FAILED' },
          });
        }
        return records[0].get('a');
      } finally {
        await session.close();
      }
    },

    async createInformant(
      _parent, { codeName, backingPersonHiveId }, { driver }) {

      // --- Validation ---
      await validateBackingPersonUniqueness(backingPersonHiveId, driver, 'Informant');
      validateNonEmptyString(codeName, 'codeName');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            MATCH (p:Person {hiveId: $backingPersonHiveId})
            CREATE (i:Informant { hiveId: newValue, codeName: $codeName })
            CREATE (i)-[:BACKED_BY { since: date() }]->(p)
            RETURN i { 
              .hiveId,
              .codeName,
              backingPerson: [p { .hiveId, .firstName, .lastName }]
            }
          `,
          { codeName, backingPersonHiveId: parseInt(backingPersonHiveId, 10) }
        );
        if (records.length === 0) {
          throw new GraphQLError('Could not create informant or find backing person.', {
            extensions: { code: 'INFORMANT_CREATION_FAILED' },
          });
        }
        const informant = records[0].get('i');
        return informant;
      } finally {
        await session.close();
      }
    },

    async createVehicle(
      _parent, { type, make, model, color }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(type, 'type');
      validateNonEmptyString(make, 'make');
      validateNonEmptyString(model, 'model');
      validateNonEmptyString(color, 'color');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (v:Vehicle { hiveId: newValue, type: $type, make: $make, model: $model, color: $color })
            RETURN v { .hiveId, .type, .make, .model, .color }
          `,
          { type, make, model, color }
        );
        return records[0].get('v');
      } finally {
        await session.close();
      }
    },

    async createOperation(
      _parent, { title, type }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(title, 'title');
      validateNonEmptyString(type, 'type');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (op:Operation { hiveId: newValue, title: $title, type: $type, creationDate: date() })
            RETURN op { .hiveId, .title, .type, .creationDate }
          `,
          { title, type }
        );
        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async createTask(
      _parent, { title, level, description, priority, assignedAgentHiveId }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(title, 'title');
      validateClearanceLevel(level);
      if (priority) {
        validateTaskPriority(priority);
      }
      if (assignedAgentHiveId) {
        await validateAgentExists(driver, assignedAgentHiveId, 'task assignment');
      }
      // --- End Validation ---

      const session = driver.session();
      try {
        // Build the parameters object, only including non-undefined values
        const params = {
          title,
          level,
          assignedAgentHiveId: assignedAgentHiveId ? parseInt(assignedAgentHiveId, 10) : null
        };

        // Only add description and priority if they are provided
        if (description !== undefined) {
          params.description = description;
        }
        if (priority !== undefined) {
          params.priority = priority;
        }

        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (t:Task {
              hiveId: newValue,
              title: $title,
              level: $level,
              ${description !== undefined ? 'description: $description,' : ''}
              priority: ${priority !== undefined ? '$priority' : '"MEDIUM"'},
              createdAt: date()
            })
            ${assignedAgentHiveId ? `
            WITH t
            MATCH (a:Agent {hiveId: $assignedAgentHiveId})
            CREATE (t)-[:ASSIGNED_TO { since: date() }]->(a)
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: [a { .hiveId, .username, .role }]
            }` : `
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: []
            }`}
          `,
          params
        );
        return records[0].get('t');
      } finally {
        await session.close();
      }
    },


  // Delete

    async deleteOrganization(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (o:Organization {hiveId: $hiveId})
            WITH o, o.hiveId AS idToDelete
            DETACH DELETE o
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        if (records.length === 0) {
          throw new Error(`Organization with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deletePerson(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $hiveId})
            WITH p, p.hiveId AS idToDelete
            DETACH DELETE p
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        if (records.length === 0) {
          throw new Error(`Person with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteCase(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (c:Case {hiveId: $hiveId})
            WITH c, c.hiveId AS idToDelete
            DETACH DELETE c
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        if (records.length === 0) {
          throw new Error(`Case with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteAgent(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (a:Agent {hiveId: $hiveId})
            WITH a, a.hiveId AS idToDelete
            DETACH DELETE a
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        
        if (records.length === 0) {
          throw new Error(`Agent with hiveId ${hiveId} not found`);
        }
        
        return {
          hiveId: records[0].get('deletedHiveId').toString(),
          deleted: true
        };
      } finally {
        await session.close();
      }
    },

    async deleteInformant(
      _parent, { hiveId }, { driver }) {
      // --- Validation ---
      await validateInformantExists(driver, hiveId, 'deleting informant');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (i:Informant {hiveId: $hiveId})
            WITH i, i.hiveId AS idToDelete
            DETACH DELETE i
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        if (records.length === 0) {
          throw new GraphQLError(`Informant with hiveId ${hiveId} not found`, {
            extensions: { code: 'NOT_FOUND', argumentName: 'hiveId', providedValue: hiveId },
          });
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteVehicle(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (v:Vehicle {hiveId: $hiveId})
            WITH v, v.hiveId AS idToDelete
            DETACH DELETE v
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        if (records.length === 0) {
          throw new Error(`Vehicle with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteOperation(
      _parent, { hiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, hiveId, 'delete operation');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $hiveId})
            WITH op, op.hiveId AS idToDelete
            DETACH DELETE op
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteTask(
      _parent, { hiveId }, { driver }) {

      // --- Validation ---
      await validateTaskExists(driver, hiveId, 'delete task');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $hiveId})
            WITH t, t.hiveId AS idToDelete
            DETACH DELETE t
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId, 10) }
        );
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

  // Update

    async updateOrganization(
      _parent, { hiveId, name, foundingDate }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (o:Organization {hiveId: $hiveId})
            SET o.name = $name, o.foundingDate = $foundingDate
            RETURN o { .hiveId, .name, .foundingDate }
          `,
          { hiveId: parseInt(hiveId, 10), name, foundingDate }
        );
        
        if (records.length === 0) {
          throw new Error(`Organization with hiveId ${hiveId} not found`);
        }
        
        return records[0].get('o');
      } finally {
        await session.close();
      }
    },

    async updatePerson(
      _parent, { hiveId, firstName, lastName, dateOfBirth }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(firstName, 'firstName');
      validateNonEmptyString(lastName, 'lastName');
      validateDateOfBirth(dateOfBirth);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $hiveId})
            SET p.firstName = $firstName, p.lastName = $lastName, p.dateOfBirth = $dateOfBirth
            RETURN p { .hiveId, .firstName, .lastName, .dateOfBirth }
          `,
          { hiveId: parseInt(hiveId, 10), firstName, lastName, dateOfBirth }
        );
        
        if (records.length === 0) {
          throw new Error(`Person with hiveId ${hiveId} not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async updateCase(
      _parent, { hiveId, title, status }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(title, 'title');
      if (status !== undefined) {
        validateCaseStatus(status);
      }
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (c:Case {hiveId: $hiveId})
            SET c.title = $title, c.status = $status
            RETURN c { .hiveId, .title, .creationDate, .status }
          `,
          { hiveId: parseInt(hiveId, 10), title, status }
        );
        if (records.length === 0) {
          throw new Error(`Case with hiveId ${hiveId} not found`);
        }
        return records[0].get('c');
      } finally {
        await session.close();
      }
    },

    async updateAgent(
      _parent, { hiveId, username, password, role, clearance }, { driver }) {

      // --- Validation ---
      if (username !== undefined) {
        validateNonEmptyString(username, 'username');
      }
      if (password !== undefined) {
        validateNonEmptyString(password, 'password');
      }
      if (role !== undefined) {
        validateAgentRole(role);
      }
      if (clearance !== undefined) {
        validateClearanceLevel(clearance);
      }
      // --- End Validation ---

      const session = driver.session();
      try {
        const hashedPassword = password !== undefined ? await bcrypt.hash(password, 10) : undefined;
        const { records } = await session.run(
          `
            MATCH (a:Agent {hiveId: $hiveId})
            SET a.username = COALESCE($username, a.username),
                a.password = COALESCE($password, a.password),
                a.role = COALESCE($role, a.role)
            WITH a
            MATCH (a)-[:BACKED_BY]->(bp:Person)
            RETURN a {
              .username,
              .role,
              .clearance,
              hiveId: toString(a.hiveId),
              backingPerson: [bp { .hiveId, .firstName, .lastName }]
            }
          `,
          { hiveId: parseInt(hiveId, 10), username, password: hashedPassword, role, clearance }
        );
        
        if (records.length === 0) {
          throw new Error(`Agent with hiveId ${hiveId} not found`);
        }
        
      return records[0].get('a');
      } finally {
        await session.close();
      }
    },

    async setAgentClearance(
      _parent, { agentHiveId, clearance }, { driver }) {

      // --- Validation ---
      await validateAgentExists(driver, agentHiveId, 'setting agent clearance');
      validateClearanceLevel(clearance);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (a:Agent {hiveId: $agentHiveId})
            SET a.clearance = $clearance
            WITH a
            MATCH (a)-[:BACKED_BY]->(bp:Person)
            RETURN a {
              .username,
              .role,
              .clearance,
              hiveId: toString(a.hiveId),
              backingPerson: [bp { .hiveId, .firstName, .lastName }]
            }
          `,
          { agentHiveId: parseInt(agentHiveId, 10), clearance }
        );

        if (records.length === 0) {
          throw new Error(`Agent with hiveId ${agentHiveId} not found or has no backing person`);
        }

        return records[0].get('a');
      } finally {
        await session.close();
      }
    },

    async updateInformant(
      _parent, { hiveId, codeName }, { driver }) {

      // --- Validation ---
      await validateInformantExists(driver, hiveId, 'updating informant');
      if (codeName !== undefined) {
        validateNonEmptyString(codeName, 'codeName');
      }
      // --- End Validation ---

      const session = driver.session();
      try {
        const result = await session.run(
          `
            MATCH (i:Informant {hiveId: $hiveId})
            SET i.codeName = coalesce($codeName, i.codeName)
            WITH i
            MATCH (i)-[:BACKED_BY]->(p:Person)
            RETURN i { 
                .hiveId, 
                .codeName, 
                backingPerson: [p { .hiveId, .firstName, .lastName }]
            }
          `,
          { hiveId: parseInt(hiveId, 10), codeName }
        );
 
        if (result.records.length === 0) {
          throw new GraphQLError(`Informant with hiveId ${hiveId} not found.`, {
            extensions: { code: 'NOT_FOUND', argumentName: 'hiveId', providedValue: hiveId },
          });
        }
        const informant = result.records[0].get('i');
        return informant;
      } finally {
        await session.close();
      }
    },

    async updateVehicleColor(
      _parent, { hiveId, color }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(color, 'color');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (v:Vehicle {hiveId: $hiveId})
            SET v.color = $color
            RETURN v { .hiveId, .type, .make, .model, .color }
          `,
          { hiveId: parseInt(hiveId, 10), color }
        );
        if (records.length === 0) {
          throw new Error(`Vehicle with hiveId ${hiveId} not found`);
        }
        return records[0].get('v');
      } finally {
        await session.close();
      }
    },

    async updateOperation(
      _parent, { hiveId, title, type }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, hiveId, 'update operation');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $hiveId})
            SET op.title = COALESCE($title, op.title),
                op.type = COALESCE($type, op.type)
            RETURN op { .hiveId, .title, .type, .creationDate }
          `,
          { hiveId: parseInt(hiveId, 10), title, type }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

  // Relationship mutations

    async addMemberToOrganization(
      _parent, { personHiveId, organizationHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (o:Organization {hiveId: $organizationHiveId})
            MERGE (p)-[r:MEMBER_OF]->(o) ON CREATE SET r.since = date()
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              .dateOfBirth,
              memberOf: [(p)-[:MEMBER_OF]->(org) | org {.hiveId, .name, .foundingDate}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            organizationHiveId: parseInt(organizationHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Organization not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeMemberFromOrganization(
      _parent, { personHiveId, organizationHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (o:Organization {hiveId: $organizationHiveId})
            MATCH (p)-[r:MEMBER_OF]->(o)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              .dateOfBirth,
              memberOf: [(p)-[:MEMBER_OF]->(org) | org {.hiveId, .name, .foundingDate}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            organizationHiveId: parseInt(organizationHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Organization not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async addSuspectToCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (p)-[r:SUSPECT_IN]->(c) ON CREATE SET r.since = date()
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              suspectIn: [(p)-[:SUSPECT_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            caseHiveId: parseInt(caseHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeSuspectFromCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (p)-[r:SUSPECT_IN]->(c)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              suspectIn: [(p)-[:SUSPECT_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            caseHiveId: parseInt(caseHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found, or no relationship exists`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async addVictimToCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (p)-[r:VICTIM_IN]->(c) ON CREATE SET r.since = date()
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              victimIn: [(p)-[:VICTIM_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            caseHiveId: parseInt(caseHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeVictimFromCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (p)-[r:VICTIM_IN]->(c)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              victimIn: [(p)-[:VICTIM_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            caseHiveId: parseInt(caseHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found, or no relationship exists`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async addWitnessToCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (p)-[r:WITNESS_IN]->(c) ON CREATE SET r.since = date()
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              witnessIn: [(p)-[:WITNESS_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            caseHiveId: parseInt(caseHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeWitnessFromCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (p)-[r:WITNESS_IN]->(c)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              witnessIn: [(p)-[:WITNESS_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId, 10), 
            caseHiveId: parseInt(caseHiveId, 10) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found, or no relationship exists`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async setVehicleOwner(
      _parent, { vehicleHiveId, ownerHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (v:Vehicle {hiveId: $vehicleHiveId})
            MATCH (newOwner) WHERE newOwner.hiveId = $ownerHiveId AND (newOwner:Person OR newOwner:Organization)
            // If both vehicle and new owner are found, proceed
            WITH v, newOwner
            OPTIONAL MATCH (v)-[oldRel:OWNED_BY]->()
            DELETE oldRel
            CREATE (v)-[newRel:OWNED_BY { since: date() }]->(newOwner)
            RETURN v {
              .hiveId,
              .type,
              .make,
              .model,
              .color,
              owner: [newOwner {
                __typename: CASE WHEN newOwner:Person THEN 'Person' ELSE 'Organization' END,
                .hiveId,
                firstName: newOwner.firstName,
                lastName: newOwner.lastName,
                dateOfBirth: newOwner.dateOfBirth,
                name: newOwner.name,
                foundingDate: newOwner.foundingDate
              }]
            }
          `,
          { vehicleHiveId: parseInt(vehicleHiveId, 10), ownerHiveId: parseInt(ownerHiveId, 10) }
        );

        if (records.length === 0) {
          throw new GraphQLError(`Vehicle with hiveId ${vehicleHiveId} or Owner with hiveId ${ownerHiveId} not found, or operation failed.`, {
            extensions: { code: 'SET_OWNER_FAILED' },
          });
        }
        return records[0].get('v');
      } finally {
        await session.close();
      }
    },

    async setInformantHandler(
      _parent, { informantHiveId, agentHiveId }, { driver }) {
      // --- Validation ---
      await validateInformantExists(driver, informantHiveId, 'setting informant handler');
      await validateAgentExists(driver, agentHiveId, 'setting informant handler');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (i:Informant {hiveId: $informantHiveId})
            MATCH (a:Agent {hiveId: $agentHiveId}) // Try to find the new agent first
            // If both informant and new agent are found, proceed
            WITH i, a
            OPTIONAL MATCH (i)-[oldRel:HANDLED_BY]->() // Find any existing handler
            DELETE oldRel // Delete it
            CREATE (i)-[newRel:HANDLED_BY { since: date() }]->(a) // Create the new relationship
            WITH i, a
            MATCH (a)-[:BACKED_BY]->(abp:Person)
            RETURN i {
              .hiveId,
              .codeName,
              backingPerson: [(i)-[:BACKED_BY]->(bp:Person) | bp {.hiveId, .firstName, .lastName}],
              handlerAgent: [a {
                .hiveId,
                .username,
                .role,
                backingPerson: [abp { .hiveId, .firstName, .lastName }]
              }]
            }
          `,
          { informantHiveId: parseInt(informantHiveId, 10), agentHiveId: parseInt(agentHiveId, 10) }
        );

        if (records.length === 0) {
          throw new GraphQLError('Could not set informant handler. Informant or Agent not found.', {
            extensions: { code: 'SET_HANDLER_FAILED' },
          });
        }
        return records[0].get('i');
      } finally {
        await session.close();
      }
    },

    async setOrganizationParent(
      _parent, { childOrganizationHiveId, parentOrganizationHiveId }, { driver }) {

      // --- Validation ---
      await validateParentOrganization(driver, childOrganizationHiveId, parentOrganizationHiveId);
      // --- End Validation ---

      const session = driver.session();
      try {
        // Remove any existing parent relationship for the child organization
        await session.run(
          `
            MATCH (:Organization)-[r:PARENT_OF]->(child:Organization {hiveId: $childOrganizationHiveId})
            DELETE r
          `,
          { childOrganizationHiveId: parseInt(childOrganizationHiveId, 10) }
        );

        // Create the new parent relationship
        const { records } = await session.run(
          `
            MATCH (child:Organization {hiveId: $childOrganizationHiveId})
            MATCH (parent:Organization {hiveId: $parentOrganizationHiveId})
            MERGE (parent)-[r:PARENT_OF]->(child) ON CREATE SET r.since = date()
            RETURN child {
              .hiveId,
              .name,
              parentOrganization: [(orgParent)-[:PARENT_OF]->(child) | orgParent { .name, .hiveId }],
              childOrganizations: [(child)-[:PARENT_OF]->(orgChild) | orgChild { .name, .hiveId }]
            }
          `,
          {
            childOrganizationHiveId: parseInt(childOrganizationHiveId, 10),
            parentOrganizationHiveId: parseInt(parentOrganizationHiveId, 10)
          }
        );
        if (records.length === 0) {
          throw new GraphQLError('Child or Parent Organization not found.', {
            extensions: { code: 'ORGANIZATION_NOT_FOUND' },
          });
        }
        return records[0].get('child');
      } finally {
        await session.close();
      }
    },

    async addTargetToOperation(
      _parent, { targetHiveId, operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'adding target');
      await validateTargetExists(driver, targetHiveId, 'adding target');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle)
            MERGE (target)-[r:TARGET_OF]->(op) ON CREATE SET r.since = date()
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              targets: [(t)-[:TARGET_OF]->(op) | t {
                __typename: CASE
                  WHEN t:Person THEN 'Person'
                  WHEN t:Organization THEN 'Organization'
                  ELSE 'Vehicle'
                END,
                .hiveId,
                firstName: t.firstName,
                lastName: t.lastName,
                dateOfBirth: t.dateOfBirth,
                name: t.name,
                foundingDate: t.foundingDate,
                type: t.type,
                make: t.make,
                model: t.model,
                color: t.color
              }]
            }
          `,
          {
            targetHiveId: parseInt(targetHiveId, 10),
            operationHiveId: parseInt(operationHiveId, 10)
          }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async removeTargetFromOperation(
      _parent, { targetHiveId, operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'removing target');
      await validateTargetExists(driver, targetHiveId, 'removing target');
      await validateTargetRelationshipExists(driver, targetHiveId, operationHiveId);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle)
            MATCH (target)-[r:TARGET_OF]->(op)
            DELETE r
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              targets: [(t)-[:TARGET_OF]->(op) | t {
                __typename: CASE
                  WHEN t:Person THEN 'Person'
                  WHEN t:Organization THEN 'Organization'
                  ELSE 'Vehicle'
                END,
                .hiveId,
                firstName: t.firstName,
                lastName: t.lastName,
                dateOfBirth: t.dateOfBirth,
                name: t.name,
                foundingDate: t.foundingDate,
                type: t.type,
                make: t.make,
                model: t.model,
                color: t.color
              }]
            }
          `,
          {
            targetHiveId: parseInt(targetHiveId, 10),
            operationHiveId: parseInt(operationHiveId, 10)
          }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async setLeadAgentForOperation(
      _parent, { agentHiveId, operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'setting lead agent');
      await validateAgentExists(driver, agentHiveId, 'setting as lead agent');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (a:Agent {hiveId: $agentHiveId})
            // If both operation and agent are found, proceed
            WITH op, a
            OPTIONAL MATCH (oldAgent:Agent)-[oldRel:LEAD_FOR]->(op)
            DELETE oldRel
            CREATE (a)-[newRel:LEAD_FOR { since: date() }]->(op)
            WITH op, a
            MATCH (a)-[:BACKED_BY]->(bp:Person)
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              leadAgent: [a {
                .hiveId,
                .username,
                .role,
                backingPerson: [bp { .hiveId, .firstName, .lastName }]
              }]
            }
          `,
          { agentHiveId: parseInt(agentHiveId, 10), operationHiveId: parseInt(operationHiveId, 10) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async removeLeadAgentFromOperation(
      _parent, { operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'removing lead agent');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            OPTIONAL MATCH (a:Agent)-[r:LEAD_FOR]->(op)
            DELETE r
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              leadAgent: []
            }
          `,
          { operationHiveId: parseInt(operationHiveId, 10) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async addScopedCaseToOperation(
      _parent, { operationHiveId, caseHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'adding scoped case');
      await validateCaseExists(driver, caseHiveId, 'adding to operation scope');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (op)-[r:SCOPED_TO]->(c) ON CREATE SET r.since = date()
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              scopedToCase: [(op)-[:SCOPED_TO]->(case) | case {
                .hiveId,
                .title,
                .creationDate,
                .status
              }]
            }
          `,
          { operationHiveId: parseInt(operationHiveId, 10), caseHiveId: parseInt(caseHiveId, 10) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async removeScopedCaseFromOperation(
      _parent, { operationHiveId, caseHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'removing scoped case');
      await validateCaseExists(driver, caseHiveId, 'removing from operation scope');
      await validateCaseScopeRelationshipExists(driver, operationHiveId, caseHiveId);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (op)-[r:SCOPED_TO]->(c)
            DELETE r
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              scopedToCase: [(op)-[:SCOPED_TO]->(case) | case {
                .hiveId,
                .title,
                .creationDate,
                .status
              }]
            }
          `,
          { operationHiveId: parseInt(operationHiveId, 10), caseHiveId: parseInt(caseHiveId, 10) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async assignAgentToTask(
      _parent, { taskHiveId, agentHiveId }, { driver }) {

      // --- Validation ---
      await validateTaskExists(driver, taskHiveId, 'assigning agent to task');
      await validateAgentExists(driver, agentHiveId, 'assigning to task');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $taskHiveId})
            MATCH (a:Agent {hiveId: $agentHiveId})
            // Remove any existing assignment
            OPTIONAL MATCH (t)-[oldRel:ASSIGNED_TO]->()
            DELETE oldRel
            // Create new assignment
            CREATE (t)-[:ASSIGNED_TO { since: date() }]->(a)
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: [a { .hiveId, .username, .role }]
            }
          `,
          { taskHiveId: parseInt(taskHiveId, 10), agentHiveId: parseInt(agentHiveId, 10) }
        );

        return records[0].get('t');
      } finally {
        await session.close();
      }
    },

    async removeAgentFromTask(
      _parent, { taskHiveId }, { driver }) {

      // --- Validation ---
      await validateTaskExists(driver, taskHiveId, 'removing agent from task');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $taskHiveId})
            OPTIONAL MATCH (t)-[r:ASSIGNED_TO]->()
            DELETE r
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: []
            }
          `,
          { taskHiveId: parseInt(taskHiveId, 10) }
        );

        return records[0].get('t');
      } finally {
        await session.close();
      }
    },

    async setTaskPriority(
      _parent, { taskHiveId, priority }, { driver }) {

      // --- Validation ---
      await validateTaskExists(driver, taskHiveId, 'setting task priority');
      validateTaskPriority(priority);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $taskHiveId})
            SET t.priority = $priority
            WITH t
            OPTIONAL MATCH (t)-[:ASSIGNED_TO]->(a:Agent)
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: [a { .hiveId, .username, .role }]
            }
          `,
          { taskHiveId: parseInt(taskHiveId, 10), priority }
        );

        return records[0].get('t');
      } finally {
        await session.close();
      }
    },

    async setTaskLevel(
      _parent, { taskHiveId, level }, { driver }) {

      // --- Validation ---
      await validateTaskExists(driver, taskHiveId, 'setting task level');
      validateClearanceLevel(level);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $taskHiveId})
            SET t.level = $level
            WITH t
            OPTIONAL MATCH (t)-[:ASSIGNED_TO]->(a:Agent)
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: [a { .hiveId, .username, .role }]
            }
          `,
          { taskHiveId: parseInt(taskHiveId, 10), level }
        );

        return records[0].get('t');
      } finally {
        await session.close();
      }
    },

    async setTaskScope(
      _parent, { taskHiveId, scopeHiveId }, { driver }) {

      // --- Validation ---
      await validateTaskExists(driver, taskHiveId, 'setting task scope');
      await validateTaskScopeEntityExists(driver, scopeHiveId);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (t:Task {hiveId: $taskHiveId})
            MATCH (scope) WHERE scope.hiveId = $scopeHiveId AND (scope:Case OR scope:Operation)
            WITH t, scope
            OPTIONAL MATCH (t)-[oldRel:SCOPED_TO]->()
            DELETE oldRel
            CREATE (t)-[:SCOPED_TO { since: date() }]->(scope)
            WITH t, scope
            OPTIONAL MATCH (t)-[:ASSIGNED_TO]->(a:Agent)
            RETURN t {
              .hiveId,
              .title,
              .level,
              .description,
              .priority,
              .createdAt,
              assignedAgent: [a { .hiveId, .username, .role }],
              scope: [scope {
                __typename: CASE WHEN scope:Case THEN 'Case' ELSE 'Operation' END,
                .hiveId,
                title: scope.title,
                creationDate: scope.creationDate,
                status: scope.status,
                type: scope.type
              }]
            }
          `,
          { taskHiveId: parseInt(taskHiveId, 10), scopeHiveId: parseInt(scopeHiveId, 10) }
        );

        return records[0].get('t');
      } finally {
        await session.close();
      }
    },


  // Special mutations

    async login(_parent, { username, password }, { driver, res }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (a:Agent { username: $username })
            MATCH (a)-[:BACKED_BY]->(bp:Person)
            RETURN a {
              .username,
              .role,
              .clearance,
              hiveId: toString(a.hiveId),
              backingPerson: [bp { .hiveId, .firstName, .lastName }]
            } AS agent, a.password AS password
          `,
          { username }
        );
        if (records.length === 0) {
          throw new GraphQLError('Invalid credentials', {
            extensions: { code: 'UNAUTHENTICATED' }
          });
        }
        const agent = records[0].get('agent');
        const storedHash = records[0].get('password');
        const match = await bcrypt.compare(password, storedHash);
        if (!match) {
          throw new GraphQLError('Invalid credentials', {
            extensions: { code: 'UNAUTHENTICATED' }
          });
        }
        const token = jwt.sign(
          { hiveId: agent.hiveId, username: agent.username, role: agent.role, clearance: agent.clearance },
          JWT_SECRET,
          { expiresIn: '12h' }
        );

        const csrfToken = crypto.randomBytes(24).toString('hex');

        // Set httpOnly cookie with JWT token
        const isProduction = process.env.NODE_ENV === 'production';
        res.cookie('agentAuth', token, {
          httpOnly: true,
          secure: isProduction, // Only use secure in production (HTTPS)
          sameSite: 'strict',
          maxAge: 12 * 60 * 60 * 1000, // 12 hours in milliseconds
          path: '/'
        });

        // CSRF token cookie and header
        res.cookie('csrfToken', csrfToken, {
          httpOnly: false,
          secure: isProduction,
          sameSite: 'strict',
          maxAge: 12 * 60 * 60 * 1000,
          path: '/'
        });
        res.set('x-csrf-token', csrfToken);

        // Set user data cookie (not httpOnly so frontend can read it)
        res.cookie('agentData', JSON.stringify(agent), {
          httpOnly: false,
          secure: isProduction,
          sameSite: 'strict',
          maxAge: 12 * 60 * 60 * 1000, // 12 hours in milliseconds
          path: '/'
        });

        // Return success without token (token is now in cookie)
        return { success: true, agent };
      } finally {
        await session.close();
      }
    },

    async logout(_parent, _args, { res }) {
      // Clear both authentication and user data cookies
      const isProduction = process.env.NODE_ENV === 'production';

      res.clearCookie('agentAuth', {
        httpOnly: true,
        secure: isProduction,
        sameSite: 'strict',
        path: '/'
      });

      res.clearCookie('csrfToken', {
        httpOnly: false,
        secure: isProduction,
        sameSite: 'strict',
        path: '/'
      });

      res.clearCookie('agentData', {
        httpOnly: false,
        secure: isProduction,
        sameSite: 'strict',
        path: '/'
      });

      return true;
    }
  }
};

// Export resolvers and the secret
module.exports = { resolvers, JWT_SECRET }; 
